# Manual Testing Guide for New Feedback Flow

## Overview
This guide shows you how to manually test the new feedback-before-query system to verify it works correctly.

## Prerequisites
1. ✅ FastAPI server running (`python main12.py`)
2. ✅ Redis server running and accessible
3. ✅ Valid API keys configured

## Test Scenario

### Step 1: Submit First Query
**Endpoint**: `POST /query`

**Request**:
```json
{
  "query": "What is artificial intelligence?",
  "mode": "web",
  "host": "groq",
  "model": "llama-3.3-70b-versatile",
  "api_key": "your_groq_api_key"
}
```

**Expected Result**:
- ✅ Query processed normally
- ✅ Response generated
- ✅ User and assistant messages stored in Redis
- ✅ No feedback context used (first query)

**Redis Check**:
```bash
# Check session messages
redis-cli LRANGE "session:user_id:chat_id" 0 -1
# Should show: [user_message, assistant_message]
```

### Step 2: Submit Feedback
**Endpoint**: `POST /feedback`

**Request**:
```json
{
  "feedback_type": "thumbs-down",
  "feedback_value": "Too technical, please explain more simply",
  "timestamp": "2025-07-28T12:00:00Z"
}
```

**Expected Result**:
- ✅ Feedback stored in pending queue
- ✅ Feedback stored in analytics
- ✅ Response confirms feedback will improve future responses

**Response**:
```json
{
  "status": "success",
  "feedback": {
    "feedback_type": "thumbs-down",
    "feedback_value": "Too technical, please explain more simply",
    "timestamp": "2025-07-28T12:00:00Z",
    "message": "Feedback stored and will be used to improve future responses"
  }
}
```

**Redis Check**:
```bash
# Check pending feedback
redis-cli LRANGE "pending_feedback:user_id:chat_id" 0 -1
# Should show: [feedback_entry]

# Check analytics feedback
redis-cli LRANGE "feedback:user_id:chat_id" 0 -1
# Should show: [feedback_entry]
```

### Step 3: Submit Second Query (With Feedback Context)
**Endpoint**: `POST /query`

**Request**:
```json
{
  "query": "Explain machine learning concepts",
  "mode": "web",
  "host": "groq", 
  "model": "llama-3.3-70b-versatile",
  "api_key": "your_groq_api_key"
}
```

**Expected Result**:
- ✅ Pending feedback consumed and used as context
- ✅ Feedback stored in session before user query
- ✅ Enhanced response based on feedback context
- ✅ Pending feedback queue cleared

**Redis Check After Step 3**:
```bash
# Check session messages
redis-cli LRANGE "session:user_id:chat_id" 0 -1
# Should show: [user_msg1, assistant_msg1, feedback_entry, user_msg2, assistant_msg2]

# Check pending feedback (should be empty)
redis-cli LRANGE "pending_feedback:user_id:chat_id" 0 -1
# Should show: []

# Check analytics feedback (should still exist)
redis-cli LRANGE "feedback:user_id:chat_id" 0 -1
# Should show: [feedback_entry]
```

## Expected Flow Visualization

```
Timeline: User Actions → Redis Storage

1. Query 1 → [user1, assistant1]
2. Feedback → [user1, assistant1] + pending:[feedback] + analytics:[feedback]
3. Query 2 → [user1, assistant1, feedback, user2, assistant2] + pending:[] + analytics:[feedback]
```

## Key Verification Points

### ✅ Feedback Storage Structure
The feedback entry should have this structure:
```json
{
  "id": "unique_id",
  "role": "feedback",
  "feedback_type": "thumbs-down",
  "feedback_value": "Too technical, please explain more simply",
  "timestamp": "2025-07-28T12:00:00Z",
  "context": {
    "user_query": "What is artificial intelligence?",
    "assistant_response": "AI is artificial intelligence...",
    "query_metadata": {...},
    "response_metadata": {...}
  },
  "metadata": {
    "chat_id": "chat_id",
    "user_id": "user_id", 
    "for_future_improvement": true
  }
}
```

### ✅ Feedback Context Integration
Check server logs for these messages:
```
INFO: Using 1 feedback entries as context for query improvement
INFO: 📝 Including feedback context for answer improvement
INFO: Consumed 1 pending feedback entries for chat_id=...
```

### ✅ Enhanced Response Quality
The second response should:
- Be simpler/clearer if feedback was "too technical"
- Address the specific feedback provided
- Show improvement based on user preferences

## Testing Different Feedback Types

### Positive Feedback Test
```json
{
  "feedback_type": "thumbs-up",
  "feedback_value": "Great explanation, very clear!",
  "timestamp": "2025-07-28T12:00:00Z"
}
```

### Specific Improvement Feedback
```json
{
  "feedback_type": "improvement",
  "feedback_value": "Please include more examples next time",
  "timestamp": "2025-07-28T12:00:00Z"
}
```

## Troubleshooting

### Issue: Feedback not consumed
**Check**: 
- Server logs for consumption messages
- Redis pending feedback queue
- Session messages for feedback entries

### Issue: No improvement in responses
**Check**:
- Feedback context formatting in logs
- Enhanced history being passed to LLM
- LLM receiving feedback context

### Issue: Duplicate messages
**Check**:
- Duplicate prevention logic
- Session message count
- Clean duplicates endpoint: `POST /clean_duplicates`

## API Endpoints for Testing

### Check Redis Status
```bash
curl -X GET "http://localhost:8000/redis_status"
```

### Clean Duplicates (if needed)
```bash
curl -X POST "http://localhost:8000/clean_duplicates"
```

### Get Chat History
```bash
curl -X GET "http://localhost:8000/history/chat_id"
```

## Success Criteria

✅ **Feedback Storage**: Feedback stored in pending queue and analytics
✅ **Feedback Consumption**: Pending feedback consumed during next query
✅ **Context Integration**: Feedback used as context for LLM
✅ **Response Improvement**: Subsequent responses show improvement
✅ **Queue Management**: Pending queue cleared after consumption
✅ **No Duplicates**: Clean message storage without duplicates

## Expected Logs

```
INFO: Feedback stored for future improvement: chat_id=..., type=thumbs-down
INFO: Feedback will be used as context for next query in this session
INFO: Using 1 feedback entries as context for query improvement
INFO: 📝 Including feedback context for answer improvement
INFO: Consumed 1 pending feedback entries for chat_id=...
```

This new system ensures that user feedback actively improves future responses, creating a learning conversation system! 🚀
