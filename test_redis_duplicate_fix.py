#!/usr/bin/env python3
"""
Test script to verify Redis duplicate message fixes.
This script tests the new duplicate prevention and cleaning functionality.
"""

import os
import json
import redis
import logging
from datetime import datetime
from bson import ObjectId

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_redis_connection():
    """Test Redis connection using environment variables"""
    try:
        redis_client = redis.Redis(
            host=os.getenv("REDIS_HOST", "localhost"),
            port=int(os.getenv("REDIS_PORT", 6379)),
            password=os.getenv("REDIS_PASSWORD"),
            db=int(os.getenv("REDIS_DB", 0)),
            ssl=os.getenv("REDIS_SSL", "false").lower() == "true",
            ssl_cert_reqs=None,
            ssl_check_hostname=False,
            decode_responses=True,
            socket_connect_timeout=10,
            socket_timeout=10,
            retry_on_timeout=True
        )
        
        response = redis_client.ping()
        logger.info(f"✅ Redis connection successful: PING = {response}")
        return redis_client
    except Exception as e:
        logger.error(f"❌ Redis connection failed: {e}")
        return None

def create_test_messages(redis_client, user_id: str, chat_id: str):
    """Create test messages including duplicates"""
    session_key = f"session:{user_id}:{chat_id}"
    
    # Clear existing session
    redis_client.delete(session_key)
    
    # Create test messages with duplicates
    messages = [
        {
            "id": str(ObjectId()),
            "role": "user",
            "content": "What is AI?",
            "timestamp": datetime.now().isoformat(),
            "metadata": {"chat_id": chat_id, "mode": "web"}
        },
        {
            "id": str(ObjectId()),
            "role": "assistant",
            "content": "AI stands for Artificial Intelligence...",
            "timestamp": datetime.now().isoformat(),
            "metadata": {"chat_id": chat_id, "mode": "web"}
        },
        # Duplicate user message
        {
            "id": str(ObjectId()),
            "role": "user",
            "content": "What is AI?",
            "timestamp": datetime.now().isoformat(),
            "metadata": {"chat_id": chat_id, "mode": "web"}
        },
        # Duplicate assistant message with feedback
        {
            "id": str(ObjectId()),
            "role": "assistant",
            "content": "AI stands for Artificial Intelligence...",
            "timestamp": datetime.now().isoformat(),
            "metadata": {"chat_id": chat_id, "mode": "web"},
            "feedback": {
                "feedback_type": "thumbs-up",
                "feedback_value": "True",
                "timestamp": datetime.now().isoformat()
            }
        },
        # Another unique message
        {
            "id": str(ObjectId()),
            "role": "user",
            "content": "Tell me about machine learning",
            "timestamp": datetime.now().isoformat(),
            "metadata": {"chat_id": chat_id, "mode": "web"}
        },
        {
            "id": str(ObjectId()),
            "role": "assistant",
            "content": "Machine learning is a subset of AI...",
            "timestamp": datetime.now().isoformat(),
            "metadata": {"chat_id": chat_id, "mode": "web"}
        }
    ]
    
    # Store all messages
    for msg in messages:
        redis_client.rpush(session_key, json.dumps(msg))
    
    redis_client.expire(session_key, 3600)
    logger.info(f"Created {len(messages)} test messages (including duplicates)")
    return len(messages)

def clean_duplicate_messages(redis_client, user_id: str, chat_id: str) -> bool:
    """
    Clean duplicate messages from a Redis session.
    This is the same function from main12.py for testing.
    """
    try:
        session_key = f"session:{user_id}:{chat_id}"
        all_msgs = redis_client.lrange(session_key, 0, -1)
        
        if not all_msgs:
            return False
        
        parsed_messages = [json.loads(msg) for msg in all_msgs]
        cleaned_messages = []
        seen_pairs = set()
        
        i = 0
        while i < len(parsed_messages):
            msg = parsed_messages[i]
            
            if msg.get('role') == 'user':
                # Check if this is part of a user-assistant pair
                if (i + 1 < len(parsed_messages) and 
                    parsed_messages[i + 1].get('role') == 'assistant'):
                    
                    user_content = msg.get('content', '')
                    assistant_content = parsed_messages[i + 1].get('content', '')
                    pair_key = f"{user_content}||{assistant_content}"
                    
                    if pair_key not in seen_pairs:
                        # First occurrence of this pair, keep it
                        seen_pairs.add(pair_key)
                        cleaned_messages.append(msg)
                        cleaned_messages.append(parsed_messages[i + 1])
                    else:
                        # Duplicate pair found, but check if the duplicate has feedback
                        if 'feedback' in parsed_messages[i + 1]:
                            # If duplicate has feedback, update the original with feedback
                            for j, clean_msg in enumerate(cleaned_messages):
                                if (clean_msg.get('role') == 'assistant' and 
                                    clean_msg.get('content') == assistant_content and
                                    'feedback' not in clean_msg):
                                    cleaned_messages[j]['feedback'] = parsed_messages[i + 1]['feedback']
                                    break
                        logger.info(f"Removed duplicate message pair for chat_id={chat_id}")
                    
                    i += 2  # Skip both user and assistant messages
                else:
                    # Standalone user message
                    cleaned_messages.append(msg)
                    i += 1
            else:
                # Non-user message (standalone assistant, system, etc.)
                cleaned_messages.append(msg)
                i += 1
        
        # If we found duplicates, update Redis
        if len(cleaned_messages) < len(parsed_messages):
            redis_client.delete(session_key)
            for msg in cleaned_messages:
                redis_client.rpush(session_key, json.dumps(msg))
            redis_client.expire(session_key, 3600)
            
            logger.info(f"Cleaned {len(parsed_messages) - len(cleaned_messages)} duplicate messages from chat_id={chat_id}")
            return True
        
        return False
        
    except Exception as e:
        logger.error(f"Error cleaning duplicate messages: {str(e)}")
        return False

def display_messages(redis_client, user_id: str, chat_id: str, title: str):
    """Display all messages in a session"""
    session_key = f"session:{user_id}:{chat_id}"
    all_msgs = redis_client.lrange(session_key, 0, -1)
    
    print(f"\n{title}")
    print("=" * 50)
    
    if not all_msgs:
        print("No messages found")
        return 0
    
    for i, msg_str in enumerate(all_msgs, 1):
        msg = json.loads(msg_str)
        role = msg.get('role', 'unknown')
        content = msg.get('content', '')[:50] + "..." if len(msg.get('content', '')) > 50 else msg.get('content', '')
        feedback = " [FEEDBACK]" if 'feedback' in msg else ""
        print(f"{i:2d}) {role:9s}: {content}{feedback}")
    
    return len(all_msgs)

def main():
    """Main test function"""
    print("🧪 Testing Redis Duplicate Message Fixes")
    print("=" * 60)
    
    # Test Redis connection
    redis_client = test_redis_connection()
    if not redis_client:
        print("❌ Cannot proceed without Redis connection")
        return
    
    # Test data
    user_id = "test_user_123"
    chat_id = str(ObjectId())
    
    print(f"\n📝 Test Parameters:")
    print(f"   User ID: {user_id}")
    print(f"   Chat ID: {chat_id}")
    
    # Step 1: Create test messages with duplicates
    print(f"\n🔧 Step 1: Creating test messages with duplicates...")
    original_count = create_test_messages(redis_client, user_id, chat_id)
    
    # Step 2: Display original messages
    before_count = display_messages(redis_client, user_id, chat_id, "BEFORE CLEANING")
    
    # Step 3: Clean duplicates
    print(f"\n🧹 Step 3: Cleaning duplicate messages...")
    cleaned = clean_duplicate_messages(redis_client, user_id, chat_id)
    
    # Step 4: Display cleaned messages
    after_count = display_messages(redis_client, user_id, chat_id, "AFTER CLEANING")
    
    # Step 5: Results
    print(f"\n📊 Results:")
    print(f"   Original messages: {before_count}")
    print(f"   After cleaning: {after_count}")
    print(f"   Messages removed: {before_count - after_count}")
    print(f"   Duplicates found: {'Yes' if cleaned else 'No'}")
    
    # Step 6: Verify feedback preservation
    session_key = f"session:{user_id}:{chat_id}"
    all_msgs = redis_client.lrange(session_key, 0, -1)
    feedback_count = 0
    for msg_str in all_msgs:
        msg = json.loads(msg_str)
        if 'feedback' in msg:
            feedback_count += 1
    
    print(f"   Feedback preserved: {feedback_count} message(s)")
    
    # Cleanup
    redis_client.delete(session_key)
    print(f"\n✅ Test completed successfully!")
    print(f"   Test session cleaned up")

if __name__ == "__main__":
    main()
