#!/usr/bin/env python3
"""
Simple test script for Redis feedback functions without user input.
Tests the new feedback-before-query system directly.
"""

import os
import json
import redis
import logging
from datetime import datetime
from bson import ObjectId

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_redis_connection():
    """Test Redis connection using environment variables"""
    try:
        redis_client = redis.Redis(
            host=os.getenv("REDIS_HOST", "localhost"),
            port=int(os.getenv("REDIS_PORT", 6379)),
            password=os.getenv("REDIS_PASSWORD"),
            db=int(os.getenv("REDIS_DB", 0)),
            ssl=os.getenv("REDIS_SSL", "false").lower() == "true",
            ssl_cert_reqs=None,
            ssl_check_hostname=False,
            decode_responses=True,
            socket_connect_timeout=10,
            socket_timeout=10,
            retry_on_timeout=True
        )
        
        response = redis_client.ping()
        logger.info(f"✅ Redis connection successful: PING = {response}")
        return redis_client
    except Exception as e:
        logger.error(f"❌ Redis connection failed: {e}")
        return None

def display_redis_data(redis_client, user_id: str, chat_id: str):
    """Display all Redis data for the session"""
    session_key = f"session:{user_id}:{chat_id}"
    pending_key = f"pending_feedback:{user_id}:{chat_id}"
    feedback_key = f"feedback:{user_id}:{chat_id}"
    
    print(f"\n📊 Redis Data for User: {user_id}, Chat: {chat_id}")
    print("=" * 70)
    
    # Session messages
    session_msgs = redis_client.lrange(session_key, 0, -1)
    print(f"\n🗨️  Session Messages ({len(session_msgs)}):")
    for i, msg_str in enumerate(session_msgs, 1):
        try:
            msg = json.loads(msg_str)
            role = msg.get('role', 'unknown')
            content = msg.get('content', '')[:80] + "..." if len(msg.get('content', '')) > 80 else msg.get('content', '')
            
            if role == 'feedback':
                feedback_type = msg.get('feedback_type', 'unknown')
                print(f"  {i:2d}) FEEDBACK: {feedback_type} - {content}")
            else:
                print(f"  {i:2d}) {role.upper()}: {content}")
        except:
            print(f"  {i:2d}) [INVALID]: {msg_str[:50]}...")
    
    # Pending feedback
    pending_msgs = redis_client.lrange(pending_key, 0, -1)
    print(f"\n⏳ Pending Feedback ({len(pending_msgs)}):")
    for i, msg_str in enumerate(pending_msgs, 1):
        try:
            msg = json.loads(msg_str)
            feedback_type = msg.get('feedback_type', 'unknown')
            context = msg.get('context', {})
            query = context.get('user_query', 'N/A')[:50]
            print(f"  {i:2d}) {feedback_type} for: {query}...")
        except:
            print(f"  {i:2d}) [INVALID]: {msg_str[:50]}...")
    
    # Analytics feedback
    analytics_msgs = redis_client.lrange(feedback_key, 0, -1)
    print(f"\n📈 Analytics Feedback ({len(analytics_msgs)}):")
    for i, msg_str in enumerate(analytics_msgs, 1):
        try:
            msg = json.loads(msg_str)
            feedback_type = msg.get('feedback_type', 'unknown')
            timestamp = msg.get('timestamp', 'N/A')[:19]  # Just date and time
            print(f"  {i:2d}) {feedback_type} at {timestamp}")
        except:
            print(f"  {i:2d}) [INVALID]: {msg_str[:50]}...")

def simulate_feedback_functions(redis_client, user_id: str, chat_id: str):
    """Simulate the feedback functions from main12.py"""
    
    # Function 1: Store pending feedback (from feedback endpoint)
    def store_pending_feedback(feedback_type: str, feedback_value: str, user_query: str, assistant_response: str):
        feedback_entry = {
            "id": str(ObjectId()),
            "role": "feedback",
            "feedback_type": feedback_type,
            "feedback_value": feedback_value,
            "timestamp": datetime.now().isoformat(),
            "context": {
                "user_query": user_query,
                "assistant_response": assistant_response,
                "query_metadata": {"chat_id": chat_id},
                "response_metadata": {"chat_id": chat_id}
            },
            "metadata": {
                "chat_id": chat_id,
                "user_id": user_id,
                "for_future_improvement": True
            }
        }
        
        # Store in pending queue
        pending_key = f"pending_feedback:{user_id}:{chat_id}"
        redis_client.rpush(pending_key, json.dumps(feedback_entry))
        redis_client.expire(pending_key, 3600)
        
        # Store in analytics
        feedback_key = f"feedback:{user_id}:{chat_id}"
        redis_client.rpush(feedback_key, json.dumps(feedback_entry))
        redis_client.expire(feedback_key, 3600)
        
        print(f"✅ Stored feedback: {feedback_type} = '{feedback_value}'")
        return feedback_entry
    
    # Function 2: Consume pending feedback (from query processing)
    def consume_pending_feedback():
        pending_key = f"pending_feedback:{user_id}:{chat_id}"
        pending_feedbacks = redis_client.lrange(pending_key, 0, -1)
        
        if pending_feedbacks:
            feedback_list = [json.loads(fb) for fb in pending_feedbacks]
            redis_client.delete(pending_key)  # Clear pending queue
            
            # Store feedback in session (before query)
            session_key = f"session:{user_id}:{chat_id}"
            for feedback in feedback_list:
                redis_client.rpush(session_key, json.dumps(feedback))
            
            print(f"✅ Consumed {len(feedback_list)} pending feedback entries")
            return feedback_list
        
        print("ℹ️  No pending feedback to consume")
        return []
    
    # Function 3: Format feedback context (for LLM)
    def format_feedback_context(feedback_list):
        if not feedback_list:
            return ""
        
        context_parts = ["Previous user feedback for improvement:"]
        
        for feedback in feedback_list:
            feedback_type = feedback.get('feedback_type', 'unknown')
            feedback_value = feedback.get('feedback_value', 'unknown')
            context = feedback.get('context', {})
            
            user_query = context.get('user_query', 'N/A')
            assistant_response = context.get('assistant_response', 'N/A')
            
            if len(assistant_response) > 100:
                assistant_response = assistant_response[:100] + "..."
            
            context_parts.append(f"""
Feedback: {feedback_type} ({feedback_value})
Previous Query: {user_query}
Previous Response: {assistant_response}
""")
        
        context_parts.append("Please use this feedback to improve your response to the current query.")
        return "\n".join(context_parts)
    
    return store_pending_feedback, consume_pending_feedback, format_feedback_context

def test_complete_flow():
    """Test the complete feedback flow"""
    print("🧪 Testing New Feedback Flow - Direct Redis Functions")
    print("=" * 70)
    
    # Connect to Redis
    redis_client = test_redis_connection()
    if not redis_client:
        print("❌ Cannot proceed without Redis connection")
        return False
    
    user_id = "test_user_simple"
    chat_id = str(ObjectId())
    
    # Clean up any existing data
    session_key = f"session:{user_id}:{chat_id}"
    pending_key = f"pending_feedback:{user_id}:{chat_id}"
    feedback_key = f"feedback:{user_id}:{chat_id}"
    
    redis_client.delete(session_key)
    redis_client.delete(pending_key)
    redis_client.delete(feedback_key)
    
    print(f"\n📝 Test Parameters:")
    print(f"   User ID: {user_id}")
    print(f"   Chat ID: {chat_id}")
    
    # Get feedback functions
    store_pending_feedback, consume_pending_feedback, format_feedback_context = simulate_feedback_functions(redis_client, user_id, chat_id)
    
    # Step 1: Simulate first conversation
    print(f"\n🔄 Step 1: First conversation")
    user_msg1 = {
        "id": str(ObjectId()),
        "role": "user",
        "content": "What is artificial intelligence?",
        "timestamp": datetime.now().isoformat(),
        "metadata": {"chat_id": chat_id}
    }
    
    assistant_msg1 = {
        "id": str(ObjectId()),
        "role": "assistant",
        "content": "Artificial intelligence (AI) refers to the simulation of human intelligence in machines that are programmed to think and learn like humans. It involves complex algorithms and computational models.",
        "timestamp": datetime.now().isoformat(),
        "metadata": {"chat_id": chat_id}
    }
    
    # Store first conversation
    redis_client.rpush(session_key, json.dumps(user_msg1))
    redis_client.rpush(session_key, json.dumps(assistant_msg1))
    redis_client.expire(session_key, 3600)
    
    print("✅ Stored first conversation")
    display_redis_data(redis_client, user_id, chat_id)
    
    # Step 2: User submits feedback
    print(f"\n🔄 Step 2: User submits feedback")
    store_pending_feedback(
        "thumbs-down", 
        "Too technical, please explain more simply",
        user_msg1["content"],
        assistant_msg1["content"]
    )
    
    display_redis_data(redis_client, user_id, chat_id)
    
    # Step 3: User asks second question (feedback should be consumed)
    print(f"\n🔄 Step 3: Second query with feedback context")
    
    # Consume pending feedback (this happens in query processing)
    consumed_feedback = consume_pending_feedback()
    
    # Format feedback context
    feedback_context = format_feedback_context(consumed_feedback)
    print(f"\n📋 Feedback Context Generated:")
    print(feedback_context[:200] + "..." if len(feedback_context) > 200 else feedback_context)
    
    # Store second conversation
    user_msg2 = {
        "id": str(ObjectId()),
        "role": "user",
        "content": "Explain machine learning",
        "timestamp": datetime.now().isoformat(),
        "metadata": {"chat_id": chat_id}
    }
    
    assistant_msg2 = {
        "id": str(ObjectId()),
        "role": "assistant",
        "content": "Machine learning is a simple way for computers to learn patterns from data, like how you learn to recognize faces. Instead of being programmed with specific rules, the computer looks at examples and figures out patterns on its own.",
        "timestamp": datetime.now().isoformat(),
        "metadata": {"chat_id": chat_id, "improved_with_feedback": True}
    }
    
    redis_client.rpush(session_key, json.dumps(user_msg2))
    redis_client.rpush(session_key, json.dumps(assistant_msg2))
    
    print("✅ Stored second conversation (improved with feedback)")
    display_redis_data(redis_client, user_id, chat_id)
    
    # Step 4: Submit positive feedback
    print(f"\n🔄 Step 4: Submit positive feedback")
    store_pending_feedback(
        "thumbs-up",
        "Much better! Simple and clear explanation",
        user_msg2["content"],
        assistant_msg2["content"]
    )
    
    display_redis_data(redis_client, user_id, chat_id)
    
    # Step 5: Third query
    print(f"\n🔄 Step 5: Third query with new feedback")
    consumed_feedback2 = consume_pending_feedback()
    feedback_context2 = format_feedback_context(consumed_feedback2)
    
    print(f"\n📋 New Feedback Context:")
    print(feedback_context2[:200] + "..." if len(feedback_context2) > 200 else feedback_context2)
    
    # Final analysis
    print(f"\n📊 Final Analysis:")
    session_msgs = redis_client.lrange(session_key, 0, -1)
    pending_msgs = redis_client.lrange(pending_key, 0, -1)
    analytics_msgs = redis_client.lrange(feedback_key, 0, -1)
    
    user_count = sum(1 for msg in session_msgs if 'user' in msg)
    assistant_count = sum(1 for msg in session_msgs if 'assistant' in msg)
    feedback_count = sum(1 for msg in session_msgs if 'feedback' in msg)
    
    print(f"   Total session messages: {len(session_msgs)}")
    print(f"   User messages: {user_count}")
    print(f"   Assistant messages: {assistant_count}")
    print(f"   Feedback messages in session: {feedback_count}")
    print(f"   Pending feedback: {len(pending_msgs)}")
    print(f"   Analytics feedback: {len(analytics_msgs)}")
    
    # Cleanup
    redis_client.delete(session_key)
    redis_client.delete(pending_key)
    redis_client.delete(feedback_key)
    
    print(f"\n✅ Test completed successfully!")
    print(f"   ✓ Feedback stored before queries")
    print(f"   ✓ Feedback consumed and used as context")
    print(f"   ✓ Pending queue cleared after consumption")
    print(f"   ✓ Analytics feedback preserved")
    
    return True

if __name__ == "__main__":
    test_complete_flow()
