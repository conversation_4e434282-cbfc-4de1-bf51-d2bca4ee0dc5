#!/usr/bin/env python3
"""
Test script for the new feedback-before-query system.
This script tests the new feedback flow where:
1. User submits query -> stored normally
2. User submits feedback -> stored in pending feedback queue
3. Next query -> feedback is prepended as context and consumed
"""

import os
import json
import redis
import logging
import requests
import time
from datetime import datetime
from bson import ObjectId

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Test configuration
BASE_URL = "http://localhost:8000"
TEST_USER_ID = "test_user_feedback_flow"
TEST_CHAT_ID = str(ObjectId())

def test_redis_connection():
    """Test Redis connection using environment variables"""
    try:
        redis_client = redis.Redis(
            host=os.getenv("REDIS_HOST", "localhost"),
            port=int(os.getenv("REDIS_PORT", 6379)),
            password=os.getenv("REDIS_PASSWORD"),
            db=int(os.getenv("REDIS_DB", 0)),
            ssl=os.getenv("REDIS_SSL", "false").lower() == "true",
            ssl_cert_reqs=None,
            ssl_check_hostname=False,
            decode_responses=True,
            socket_connect_timeout=10,
            socket_timeout=10,
            retry_on_timeout=True
        )
        
        response = redis_client.ping()
        logger.info(f"✅ Redis connection successful: PING = {response}")
        return redis_client
    except Exception as e:
        logger.error(f"❌ Redis connection failed: {e}")
        return None

def display_redis_session(redis_client, user_id: str, chat_id: str, title: str):
    """Display current session messages in Redis"""
    session_key = f"session:{user_id}:{chat_id}"
    all_msgs = redis_client.lrange(session_key, 0, -1)
    
    print(f"\n{title}")
    print("=" * 60)
    
    if not all_msgs:
        print("No messages found in session")
        return
    
    for i, msg_str in enumerate(all_msgs, 1):
        try:
            msg = json.loads(msg_str)
            role = msg.get('role', 'unknown')
            content = msg.get('content', '')
            
            # Truncate long content
            if len(content) > 100:
                content = content[:100] + "..."
            
            # Check for feedback
            feedback_info = ""
            if 'feedback' in msg:
                feedback_info = f" [FEEDBACK: {msg['feedback'].get('feedback_type', 'unknown')}]"
            elif role == 'feedback':
                feedback_type = msg.get('feedback_type', 'unknown')
                feedback_info = f" [PENDING FEEDBACK: {feedback_type}]"
            
            print(f"{i:2d}) {role:10s}: {content}{feedback_info}")
            
        except json.JSONDecodeError:
            print(f"{i:2d}) [INVALID JSON]: {msg_str[:100]}...")

def display_pending_feedback(redis_client, user_id: str, chat_id: str):
    """Display pending feedback queue"""
    pending_key = f"pending_feedback:{user_id}:{chat_id}"
    pending_msgs = redis_client.lrange(pending_key, 0, -1)
    
    print(f"\nPENDING FEEDBACK QUEUE")
    print("=" * 40)
    
    if not pending_msgs:
        print("No pending feedback")
        return
    
    for i, msg_str in enumerate(pending_msgs, 1):
        try:
            msg = json.loads(msg_str)
            feedback_type = msg.get('feedback_type', 'unknown')
            context = msg.get('context', {})
            query = context.get('user_query', 'N/A')[:50]
            print(f"{i}) {feedback_type} for query: {query}...")
        except json.JSONDecodeError:
            print(f"{i}) [INVALID JSON]: {msg_str[:50]}...")

def simulate_query(query: str, chat_id: str = None):
    """Simulate a query to the API"""
    print(f"\n🔍 Sending query: '{query}'")
    
    payload = {
        "query": query,
        "chat_id": chat_id or TEST_CHAT_ID,
        "mode": "web",
        "host": "groq",
        "model": "llama-3.3-70b-versatile",
        "api_key": os.getenv("GROQ_API_KEY", "test_key")
    }
    
    try:
        response = requests.post(f"{BASE_URL}/query", json=payload, stream=True)
        if response.status_code == 200:
            print("✅ Query successful")
            # Read the streaming response
            full_response = ""
            for chunk in response.iter_content(chunk_size=1024, decode_unicode=True):
                if chunk:
                    full_response += chunk
            
            # Extract just the answer part (before source info)
            lines = full_response.split('\n')
            answer_lines = []
            for line in lines:
                if line.startswith('Source:') or line.startswith('Documents:'):
                    break
                answer_lines.append(line)
            
            answer = '\n'.join(answer_lines).strip()
            print(f"📝 Response: {answer[:200]}..." if len(answer) > 200 else f"📝 Response: {answer}")
            return True
        else:
            print(f"❌ Query failed: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ Query error: {e}")
        return False

def simulate_feedback(feedback_type: str, feedback_value: str):
    """Simulate feedback submission"""
    print(f"\n👍 Sending feedback: {feedback_type} = {feedback_value}")
    
    payload = {
        "feedback_type": feedback_type,
        "feedback_value": feedback_value,
        "timestamp": datetime.now().isoformat()
    }
    
    try:
        # Note: In a real scenario, you'd need proper session management
        # For testing, we'll assume the session is active
        response = requests.post(f"{BASE_URL}/feedback", json=payload)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Feedback submitted successfully")
            print(f"📋 Message: {result.get('feedback', {}).get('message', 'N/A')}")
            return True
        else:
            print(f"❌ Feedback failed: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ Feedback error: {e}")
        return False

def test_feedback_flow():
    """Test the complete feedback flow"""
    print("🧪 Testing New Feedback Flow")
    print("=" * 60)
    
    # Connect to Redis
    redis_client = test_redis_connection()
    if not redis_client:
        print("❌ Cannot proceed without Redis connection")
        return False
    
    # Clean up any existing test data
    session_key = f"session:{TEST_USER_ID}:{TEST_CHAT_ID}"
    pending_key = f"pending_feedback:{TEST_USER_ID}:{TEST_CHAT_ID}"
    feedback_key = f"feedback:{TEST_USER_ID}:{TEST_CHAT_ID}"
    
    redis_client.delete(session_key)
    redis_client.delete(pending_key)
    redis_client.delete(feedback_key)
    
    print(f"\n📝 Test Parameters:")
    print(f"   User ID: {TEST_USER_ID}")
    print(f"   Chat ID: {TEST_CHAT_ID}")
    print(f"   Base URL: {BASE_URL}")
    
    # Step 1: First query (no feedback context)
    print(f"\n🔄 Step 1: First query (no feedback context)")
    success = simulate_query("What is artificial intelligence?", TEST_CHAT_ID)
    if not success:
        print("❌ First query failed, cannot continue test")
        return False
    
    time.sleep(1)  # Allow processing time
    display_redis_session(redis_client, TEST_USER_ID, TEST_CHAT_ID, "AFTER FIRST QUERY")
    display_pending_feedback(redis_client, TEST_USER_ID, TEST_CHAT_ID)
    
    # Step 2: Submit feedback
    print(f"\n🔄 Step 2: Submit feedback for first query")
    success = simulate_feedback("thumbs-down", "Too technical, please explain more simply")
    if not success:
        print("❌ Feedback submission failed, cannot continue test")
        return False
    
    time.sleep(1)  # Allow processing time
    display_redis_session(redis_client, TEST_USER_ID, TEST_CHAT_ID, "AFTER FEEDBACK SUBMISSION")
    display_pending_feedback(redis_client, TEST_USER_ID, TEST_CHAT_ID)
    
    # Step 3: Second query (should include feedback context)
    print(f"\n🔄 Step 3: Second query (should include feedback context)")
    success = simulate_query("Explain machine learning concepts", TEST_CHAT_ID)
    if not success:
        print("❌ Second query failed")
        return False
    
    time.sleep(1)  # Allow processing time
    display_redis_session(redis_client, TEST_USER_ID, TEST_CHAT_ID, "AFTER SECOND QUERY (WITH FEEDBACK CONTEXT)")
    display_pending_feedback(redis_client, TEST_USER_ID, TEST_CHAT_ID)
    
    # Step 4: Submit more feedback
    print(f"\n🔄 Step 4: Submit positive feedback for second query")
    success = simulate_feedback("thumbs-up", "Much better explanation, thank you!")
    if not success:
        print("❌ Second feedback submission failed")
        return False
    
    time.sleep(1)  # Allow processing time
    display_redis_session(redis_client, TEST_USER_ID, TEST_CHAT_ID, "AFTER SECOND FEEDBACK")
    display_pending_feedback(redis_client, TEST_USER_ID, TEST_CHAT_ID)
    
    # Step 5: Third query (should include new feedback context)
    print(f"\n🔄 Step 5: Third query (should include new feedback context)")
    success = simulate_query("Tell me about neural networks", TEST_CHAT_ID)
    if not success:
        print("❌ Third query failed")
        return False
    
    time.sleep(1)  # Allow processing time
    display_redis_session(redis_client, TEST_USER_ID, TEST_CHAT_ID, "FINAL SESSION STATE")
    display_pending_feedback(redis_client, TEST_USER_ID, TEST_CHAT_ID)
    
    # Analysis
    print(f"\n📊 Test Analysis:")
    
    # Count messages by type
    all_msgs = redis_client.lrange(session_key, 0, -1)
    user_msgs = 0
    assistant_msgs = 0
    feedback_msgs = 0
    
    for msg_str in all_msgs:
        try:
            msg = json.loads(msg_str)
            role = msg.get('role', 'unknown')
            if role == 'user':
                user_msgs += 1
            elif role == 'assistant':
                assistant_msgs += 1
            elif role == 'feedback':
                feedback_msgs += 1
        except:
            pass
    
    print(f"   Total messages in session: {len(all_msgs)}")
    print(f"   User messages: {user_msgs}")
    print(f"   Assistant messages: {assistant_msgs}")
    print(f"   Feedback messages: {feedback_msgs}")
    
    # Check pending feedback (should be empty after consumption)
    pending_count = redis_client.llen(pending_key)
    print(f"   Pending feedback: {pending_count}")
    
    # Check analytics feedback
    analytics_count = redis_client.llen(feedback_key)
    print(f"   Analytics feedback entries: {analytics_count}")
    
    # Cleanup
    redis_client.delete(session_key)
    redis_client.delete(pending_key)
    redis_client.delete(feedback_key)
    
    print(f"\n✅ Test completed successfully!")
    print(f"   Expected: Feedback stored before queries and consumed as context")
    print(f"   Result: {feedback_msgs} feedback entries found in session")
    print(f"   Pending feedback consumed: {'Yes' if pending_count == 0 else 'No'}")
    
    return True

def test_redis_functions_directly():
    """Test the Redis functions directly without API calls"""
    print("\n🔧 Testing Redis Functions Directly")
    print("=" * 50)

    redis_client = test_redis_connection()
    if not redis_client:
        return False

    user_id = "direct_test_user"
    chat_id = str(ObjectId())

    # Clean up
    session_key = f"session:{user_id}:{chat_id}"
    pending_key = f"pending_feedback:{user_id}:{chat_id}"
    feedback_key = f"feedback:{user_id}:{chat_id}"

    redis_client.delete(session_key)
    redis_client.delete(pending_key)
    redis_client.delete(feedback_key)

    # Simulate storing a conversation
    user_msg = {
        "id": str(ObjectId()),
        "role": "user",
        "content": "What is AI?",
        "timestamp": datetime.now().isoformat(),
        "metadata": {"chat_id": chat_id}
    }

    assistant_msg = {
        "id": str(ObjectId()),
        "role": "assistant",
        "content": "AI is artificial intelligence...",
        "timestamp": datetime.now().isoformat(),
        "metadata": {"chat_id": chat_id}
    }

    # Store messages
    redis_client.rpush(session_key, json.dumps(user_msg))
    redis_client.rpush(session_key, json.dumps(assistant_msg))
    redis_client.expire(session_key, 3600)

    print("✅ Stored initial conversation")

    # Simulate feedback submission
    feedback_entry = {
        "id": str(ObjectId()),
        "role": "feedback",
        "feedback_type": "thumbs-down",
        "feedback_value": "Too complex",
        "timestamp": datetime.now().isoformat(),
        "context": {
            "user_query": user_msg["content"],
            "assistant_response": assistant_msg["content"],
            "query_metadata": user_msg["metadata"],
            "response_metadata": assistant_msg["metadata"]
        },
        "metadata": {
            "chat_id": chat_id,
            "user_id": user_id,
            "for_future_improvement": True
        }
    }

    # Store in pending feedback
    redis_client.rpush(pending_key, json.dumps(feedback_entry))
    redis_client.expire(pending_key, 3600)

    print("✅ Stored feedback in pending queue")

    # Test retrieval functions
    pending_feedback = redis_client.lrange(pending_key, 0, -1)
    print(f"📋 Pending feedback count: {len(pending_feedback)}")

    # Test consumption (this would happen during next query)
    if pending_feedback:
        feedback_list = [json.loads(fb) for fb in pending_feedback]
        redis_client.delete(pending_key)  # Consume

        # Store feedback in session (before next query)
        for fb in feedback_list:
            redis_client.rpush(session_key, json.dumps(fb))

        print("✅ Consumed pending feedback and stored in session")

    # Display final state
    display_redis_session(redis_client, user_id, chat_id, "FINAL DIRECT TEST STATE")

    # Cleanup
    redis_client.delete(session_key)
    redis_client.delete(pending_key)
    redis_client.delete(feedback_key)

    print("✅ Direct Redis test completed")
    return True

if __name__ == "__main__":
    print("Choose test mode:")
    print("1. Full API test (requires running server)")
    print("2. Direct Redis test (tests functions only)")

    choice = input("Enter choice (1 or 2): ").strip()

    if choice == "1":
        test_feedback_flow()
    elif choice == "2":
        test_redis_functions_directly()
    else:
        print("Invalid choice. Running direct Redis test...")
        test_redis_functions_directly()
