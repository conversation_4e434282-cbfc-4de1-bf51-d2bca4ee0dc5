#!/usr/bin/env python3
"""
Validation script to check if the new feedback implementation is correct.
This script validates the code changes without requiring Redis connection.
"""

import ast
import re

def check_file_exists_and_readable(filename):
    """Check if file exists and is readable"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()
        print(f"✅ {filename} - File exists and readable ({len(content)} characters)")
        return content
    except FileNotFoundError:
        print(f"❌ {filename} - File not found")
        return None
    except Exception as e:
        print(f"❌ {filename} - Error reading file: {e}")
        return None

def validate_feedback_endpoint(content):
    """Validate the new feedback endpoint implementation"""
    print(f"\n🔍 Validating Feedback Endpoint...")
    
    # Check if feedback endpoint exists
    if '@app.post("/feedback")' not in content:
        print("❌ Feedback endpoint not found")
        return False
    
    # Check for pending feedback storage
    if 'pending_feedback:' not in content:
        print("❌ Pending feedback storage not implemented")
        return False
    
    # Check for feedback context structure
    if '"role": "feedback"' not in content:
        print("❌ Feedback role structure not found")
        return False
    
    # Check for context storage
    if '"context":' not in content and '"user_query":' not in content:
        print("❌ Feedback context storage not implemented")
        return False
    
    print("✅ Feedback endpoint implementation looks correct")
    return True

def validate_helper_functions(content):
    """Validate helper functions for feedback processing"""
    print(f"\n🔍 Validating Helper Functions...")
    
    required_functions = [
        'get_pending_feedback',
        'consume_pending_feedback', 
        'format_feedback_context'
    ]
    
    missing_functions = []
    for func in required_functions:
        if f'def {func}(' not in content:
            missing_functions.append(func)
    
    if missing_functions:
        print(f"❌ Missing functions: {', '.join(missing_functions)}")
        return False
    
    print("✅ All required helper functions found")
    return True

def validate_query_processing(content):
    """Validate query processing integration"""
    print(f"\n🔍 Validating Query Processing Integration...")
    
    # Check for feedback consumption in query processing
    if 'consume_pending_feedback' not in content:
        print("❌ Feedback consumption not integrated in query processing")
        return False
    
    # Check for feedback context formatting
    if 'format_feedback_context' not in content:
        print("❌ Feedback context formatting not integrated")
        return False
    
    # Check for enhanced history usage
    if 'enhanced_history' not in content:
        print("❌ Enhanced history with feedback not implemented")
        return False
    
    print("✅ Query processing integration looks correct")
    return True

def validate_redis_keys(content):
    """Validate Redis key structure"""
    print(f"\n🔍 Validating Redis Key Structure...")
    
    required_keys = [
        'pending_feedback:',
        'feedback:',
        'session:'
    ]
    
    missing_keys = []
    for key in required_keys:
        if key not in content:
            missing_keys.append(key)
    
    if missing_keys:
        print(f"❌ Missing Redis keys: {', '.join(missing_keys)}")
        return False
    
    print("✅ Redis key structure looks correct")
    return True

def validate_feedback_flow_logic(content):
    """Validate the overall feedback flow logic"""
    print(f"\n🔍 Validating Feedback Flow Logic...")
    
    # Check for feedback storage before query
    if 'store_session_message(user_id, chat_id, feedback_entry)' not in content:
        print("❌ Feedback storage before query not implemented")
        return False
    
    # Check for pending feedback deletion after consumption
    if 'redis_client.delete(pending_feedback_key)' not in content:
        print("❌ Pending feedback cleanup not implemented")
        return False
    
    # Check for feedback context in LLM processing
    if 'history=enhanced_history' not in content:
        print("❌ Enhanced history not passed to LLM")
        return False
    
    print("✅ Feedback flow logic looks correct")
    return True

def check_code_syntax(content):
    """Check if the Python code has valid syntax"""
    print(f"\n🔍 Checking Python Syntax...")
    
    try:
        ast.parse(content)
        print("✅ Python syntax is valid")
        return True
    except SyntaxError as e:
        print(f"❌ Syntax error found: {e}")
        print(f"   Line {e.lineno}: {e.text}")
        return False
    except Exception as e:
        print(f"❌ Error parsing code: {e}")
        return False

def extract_function_signatures(content):
    """Extract and display key function signatures"""
    print(f"\n📋 Key Function Signatures:")
    
    # Find function definitions
    function_pattern = r'def\s+(\w+)\s*\([^)]*\):'
    functions = re.findall(function_pattern, content)
    
    feedback_functions = [f for f in functions if 'feedback' in f.lower()]
    
    for func in feedback_functions:
        print(f"   • {func}()")
    
    return len(feedback_functions)

def validate_implementation():
    """Main validation function"""
    print("🔍 Validating New Feedback Implementation")
    print("=" * 60)
    
    # Check main file
    content = check_file_exists_and_readable('main12.py')
    if not content:
        print("❌ Cannot proceed without main12.py")
        return False
    
    # Run all validations
    validations = [
        validate_feedback_endpoint(content),
        validate_helper_functions(content),
        validate_query_processing(content),
        validate_redis_keys(content),
        validate_feedback_flow_logic(content),
        check_code_syntax(content)
    ]
    
    # Extract function info
    func_count = extract_function_signatures(content)
    
    # Summary
    passed = sum(validations)
    total = len(validations)
    
    print(f"\n📊 Validation Summary:")
    print(f"   Checks passed: {passed}/{total}")
    print(f"   Feedback functions found: {func_count}")
    
    if passed == total:
        print(f"\n✅ All validations passed! Implementation looks correct.")
        print(f"\n🚀 Ready to test:")
        print(f"   1. Start your FastAPI server")
        print(f"   2. Submit a query via /query endpoint")
        print(f"   3. Submit feedback via /feedback endpoint")
        print(f"   4. Submit another query (should include feedback context)")
        print(f"   5. Check Redis for proper storage structure")
        return True
    else:
        print(f"\n❌ Some validations failed. Please review the implementation.")
        return False

def check_test_files():
    """Check if test files are available"""
    print(f"\n📁 Checking Test Files:")
    
    test_files = [
        'test_new_feedback_flow.py',
        'test_redis_feedback_simple.py',
        'NEW_FEEDBACK_FLOW_SUMMARY.md'
    ]
    
    for file in test_files:
        if check_file_exists_and_readable(file):
            pass  # Already printed in function
        else:
            print(f"⚠️  {file} not found")

if __name__ == "__main__":
    success = validate_implementation()
    check_test_files()
    
    if success:
        print(f"\n🎉 Implementation validation completed successfully!")
        print(f"   Your new feedback-before-query system is ready to use!")
    else:
        print(f"\n⚠️  Please fix the issues above before testing.")
